<script lang="ts">
  import { onMount } from 'svelte';
  import { appSettings, notepadContent, settingsLoaded } from '../stores/unifiedSettingsStore';
  import { callAIWithThinking } from '../services/apiService';
  import { showStatus } from '../stores/statusStore';
  import {
    getTextContext,
    extractSelectionContext,
    createAutocompletePrompt,
    createAnswerSelectionPrompt,
    extractCursorContext,
    createCursorInsertPrompt
  } from '../utils/textUtils';
  import type { ProcessedResponse } from '../utils/thinkingUtils';

  // Component state
  let editorElement: HTMLTextAreaElement;
  let isLoading = false;
  let lastThinking: string | null = null; // Stores the last thinking process captured
  let showThinkingPanel = false;

  // Initialize notepad with welcome message if empty
  onMount(() => {
    if ($notepadContent.trim() === "") {
      notepadContent.set("Welcome to AI Pad!\n\nType something and try the AI features.");
    }
  });

  // Handle text input changes
  const handleInput = () => {
    if (editorElement) {
      notepadContent.set(editorElement.value);
    }
  };

  // Insert text at current cursor position
  const insertTextAtCursor = (text: string) => {
    if (!editorElement) return;

    const { selectionStart: startPos, selectionEnd: endPos } = editorElement;
    const currentContent = $notepadContent;

    const newContent = currentContent.substring(0, startPos) + text + currentContent.substring(endPos);
    notepadContent.set(newContent);
    editorElement.value = newContent;

    // Position cursor after inserted text and select it
    const newCursorPos = startPos + text.length;
    editorElement.focus();
    editorElement.setSelectionRange(startPos, newCursorPos);
  };

  // Handle autocomplete functionality
  const handleAutocomplete = async () => {
    if (isLoading || !editorElement) return;

    const currentContent = $notepadContent;
    const context = getTextContext(currentContent, $appSettings.autocompleteContextLength);

    if (!context.trim()) {
      showStatus("Notepad is empty or too short for autocomplete.", "error");
      return;
    }

    isLoading = true;
    const aiResponse = await callAIWithThinking(createAutocompletePrompt(context));
    isLoading = false;

    if (aiResponse?.content) {
      // Update last thinking process
      lastThinking = aiResponse.hasThinking && aiResponse.thinking ? aiResponse.thinking : null;

      // Position cursor at end and insert response
      editorElement.focus();
      editorElement.setSelectionRange(currentContent.length, currentContent.length);
      insertTextAtCursor(" " + aiResponse.content);
    }
  };

  // Handle Replace Selection functionality
  const handleAnswerSelection = async () => {
    if (isLoading || !editorElement) return;

    const { selectionStart: startPos, selectionEnd: endPos } = editorElement;

    if (startPos === endPos) {
      showStatus("Please select some text to provide context for the AI.", "error");
      return;
    }

    // Calculate context lengths (2/3 before, 1/3 after)
    const totalContextLength = $appSettings.autocompleteContextLength;
    const beforeContextLength = Math.floor(totalContextLength * 2 / 3);
    const afterContextLength = Math.floor(totalContextLength * 1 / 3);

    // Extract context around selection
    const { beforeText, afterText } = extractSelectionContext(
      $notepadContent,
      startPos,
      endPos,
      beforeContextLength,
      afterContextLength
    );

    isLoading = true;
    const aiResponse = await callAIWithThinking(createAnswerSelectionPrompt(beforeText, afterText));
    isLoading = false;

    if (aiResponse?.content) {
      // Update last thinking process
      lastThinking = aiResponse.hasThinking && aiResponse.thinking ? aiResponse.thinking : null;

      // Replace selected text with AI response
      const currentContent = $notepadContent;
      const newContent = currentContent.substring(0, startPos) + aiResponse.content + currentContent.substring(endPos);

      notepadContent.set(newContent);
      editorElement.value = newContent;

      // Select the inserted text
      const newEndPos = startPos + aiResponse.content.length;
      editorElement.focus();
      editorElement.setSelectionRange(startPos, newEndPos);
    }
  };

  // Handle insert at cursor functionality
  const handleInsertAtCursor = async () => {
    if (isLoading || !editorElement) return;

    const cursorPos = editorElement.selectionStart;
    const totalContextLength = $appSettings.autocompleteContextLength;

    // Calculate context lengths (2/3 before, 1/3 after)
    const beforeContextLength = Math.floor(totalContextLength * 2 / 3);
    const afterContextLength = Math.floor(totalContextLength * 1 / 3);

    // Extract context around cursor
    const { beforeText, afterText } = extractCursorContext(
      $notepadContent,
      cursorPos,
      beforeContextLength,
      afterContextLength
    );

    isLoading = true;
    const aiResponse = await callAIWithThinking(createCursorInsertPrompt(beforeText, afterText));
    isLoading = false;

    if (aiResponse?.content) {
      // Update last thinking process
      lastThinking = aiResponse.hasThinking && aiResponse.thinking ? aiResponse.thinking : null;

      // Insert AI response at cursor position
      const currentContent = $notepadContent;
      const newContent = currentContent.substring(0, cursorPos) + aiResponse.content + currentContent.substring(cursorPos);

      notepadContent.set(newContent);
      editorElement.value = newContent;

      // Select the inserted text
      const newEndPos = cursorPos + aiResponse.content.length;
      editorElement.focus();
      editorElement.setSelectionRange(cursorPos, newEndPos);
    }
  };



  // Hide thinking panel when setting is disabled
  $: if (!$appSettings.showThinkingPanel) {
    showThinkingPanel = false;
  }

  // Toggle thinking panel visibility
  const toggleThinkingPanel = () => {
    if (lastThinking && $appSettings.showThinkingPanel) {
      showThinkingPanel = !showThinkingPanel;
    }
  };
</script>

{#if $settingsLoaded}
<!-- Main notepad interface -->
<div class="notepad-area-container">
  <!-- Text editor -->
  <textarea
    bind:this={editorElement}
    bind:value={$notepadContent}
    class="notepad-editor"
    on:input={handleInput}
    style="font-family: {$appSettings.fontFamily}; font-size: {$appSettings.fontSize}px; text-align: {$appSettings.textAlign}; line-height: {$appSettings.lineSpacing};"
    placeholder="Start typing your notes here..."
  ></textarea>

  <!-- AI action buttons -->
  <div class="notepad-buttons">
    <button on:click={handleAutocomplete} disabled={isLoading}>
      {isLoading ? 'Generating...' : 'Autocomplete'}
    </button>
    <button on:click={handleAnswerSelection} disabled={isLoading}>
      {isLoading ? 'Generating...' : 'Replace Selection'}
    </button>
    <button on:click={handleInsertAtCursor} disabled={isLoading}>
      {isLoading ? 'Generating...' : 'Insert at Cursor'}
    </button>

    <!-- Thinking panel toggle (only show if thinking content exists and setting is enabled) -->
    {#if lastThinking && $appSettings.showThinkingPanel}
      <button on:click={toggleThinkingPanel} class="thinking-toggle" title="View AI thinking process">
        🧠 Thinking
      </button>
    {/if}
  </div>

  <!-- AI thinking process panel -->
  {#if showThinkingPanel && currentThinking && $appSettings.showThinkingPanel}
    <div class="thinking-panel">
      <div class="thinking-header">
        <h4>AI Thinking Process</h4>
        <button on:click={() => showThinkingPanel = false} class="close-thinking">×</button>
      </div>
      <div class="thinking-content">
        <pre>{currentThinking}</pre>
      </div>
    </div>
  {/if}
</div>
{:else}
<!-- Loading state -->
<div class="notepad-area-container">
  <div class="loading-placeholder">Loading notepad...</div>
</div>
{/if}

<style>
  .notepad-area-container {
    display: flex;
    flex-direction: column;
    flex-grow: 1;
    overflow: hidden;
  }
  
  .notepad-editor {
    flex-grow: 1;
    border: none;
    outline: none;
    padding: 20px;
    resize: none;
    line-height: 1.6;
    background-color: var(--color-notepad-bg);
    border-right: 1px solid var(--color-border);
    color: var(--color-text-primary);
    transition: background-color 0.2s ease, color 0.2s ease, border-color 0.2s ease;
  }

  .notepad-buttons {
    display: flex;
    gap: 10px;
    padding: 10px 20px;
    background-color: var(--color-background);
    border-top: 1px solid var(--color-border);
    border-right: 1px solid var(--color-border);
    transition: background-color 0.2s ease, border-color 0.2s ease;
  }

  .notepad-buttons button {
    padding: 8px 16px;
    border: 1px solid var(--color-border);
    background-color: var(--color-button-secondary);
    color: var(--color-text-primary);
    cursor: pointer;
    border-radius: 3px;
    transition: all 0.2s ease;
  }

  .notepad-buttons button:hover:not(:disabled) {
    background-color: var(--color-toggle-hover);
  }
  
  .notepad-buttons button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }

  .thinking-toggle {
    background-color: var(--color-ai-generated) !important;
    border-color: var(--color-accent) !important;
    color: var(--color-accent);
  }

  .thinking-toggle:hover:not(:disabled) {
    background-color: var(--color-info) !important;
  }

  .thinking-panel {
    background: var(--color-modal-bg);
    border: 1px solid var(--color-border);
    border-radius: 6px;
    margin: 10px 20px;
    max-height: 300px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    box-shadow: 0 2px 8px var(--color-shadow);
    transition: all 0.2s ease;
  }

  .thinking-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background-color: var(--color-modal-header);
    border-bottom: 1px solid var(--color-border);
    transition: all 0.2s ease;
  }

  .thinking-header h4 {
    margin: 0;
    color: var(--color-text-primary);
    font-size: 14px;
    font-weight: 600;
  }

  .close-thinking {
    background: none;
    border: none;
    font-size: 18px;
    cursor: pointer;
    color: var(--color-text-primary);
    padding: 0;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background-color 0.2s;
  }

  .close-thinking:hover {
    background-color: var(--color-toggle-hover);
  }

  .thinking-content {
    padding: 16px;
    overflow-y: auto;
    flex: 1;
  }

  .thinking-content pre {
    white-space: pre-wrap;
    word-wrap: break-word;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 13px;
    line-height: 1.4;
    color: var(--color-text-primary);
    margin: 0;
    background: var(--color-modal-header);
    padding: 12px;
    border-radius: 4px;
    border-left: 3px solid var(--color-accent);
    transition: all 0.2s ease;
  }

  .loading-placeholder {
    padding: 40px 20px;
    text-align: center;
    color: var(--color-text-primary);
    font-style: italic;
    font-size: 16px;
    transition: color 0.2s ease;
  }
</style>
