// Text manipulation utilities for the notepad

/**
 * Gets the last `maxLength` characters of a string.
 * @param text The source string.
 * @param maxLength The maximum number of characters to return from the end of the string.
 * @returns The trailing text context.
 */
export function getTextContext(text: string, maxLength?: number): string {
  return maxLength !== undefined ? text.slice(-maxLength) : text;
}

/**
 * Extracts the text before, after, and within a selection.
 * @param fullText The entire text content.
 * @param selectionStart The starting index of the selection.
 * @param selectionEnd The ending index of the selection.
 * @param beforeContextLength The number of characters to extract before the selection.
 * @param afterContextLength The number of characters to extract after the selection.
 * @returns An object containing the text before, after, and inside the selection.
 */
export function extractSelectionContext(
  fullText: string,
  selectionStart: number,
  selectionEnd: number,
  beforeContextLength: number,
  afterContextLength: number
): { beforeText: string; afterText: string; selectedText: string } {
  return {
    selectedText: fullText.substring(selectionStart, selectionEnd),
    beforeText: fullText.substring(
      Math.max(0, selectionStart - beforeContextLength),
      selectionStart
    ),
    afterText: fullText.substring(
      selectionEnd,
      selectionEnd + afterContextLength
    )
  };
}

/**
 * Extracts the text before and after the cursor position.
 * @param fullText The entire text content.
 * @param cursorPosition The current cursor position.
 * @param beforeContextLength The number of characters to extract before the cursor.
 * @param afterContextLength The number of characters to extract after the cursor.
 * @returns An object containing the text before and after the cursor.
 */
export function extractCursorContext(
  fullText: string,
  cursorPosition: number,
  beforeContextLength: number,
  afterContextLength: number
): { beforeText: string; afterText: string } {
  return {
    beforeText: fullText.substring(
      Math.max(0, cursorPosition - beforeContextLength),
      cursorPosition
    ),
    afterText: fullText.substring(
      cursorPosition,
      cursorPosition + afterContextLength
    )
  };
}

/**
 * Creates a prompt for the AI to continue writing from the given context.
 * @param context The text context to use for the prompt.
 * @returns The formatted autocomplete prompt.
 */
export function createAutocompletePrompt(context: string): string {
  return `Continue writing the following text in the same style and tone. IMPORTANT: Respect all whitespace in the provided text exactly as it appears, including any trailing spaces. If the text ends with a space, start your continuation appropriately without adding extra spaces. Only provide the continuation, do not repeat the existing text:

${context}`;
}

/**
 * Creates a prompt for the AI to fill in the text between two segments.
 * @param beforeText The text before the selection.
 * @param afterText The text after the selection.
 * @returns The formatted Replace Selection prompt.
 */
export function createAnswerSelectionPrompt(beforeText: string, afterText: string): string {
  return `Provide text that would naturally fit between the following two text segments. Write in the same style and tone as the surrounding text. IMPORTANT: Respect all whitespace in the provided text exactly as it appears, including any trailing or leading spaces. Your response should connect naturally with the existing whitespace. Only provide the connecting text, nothing else.

Text before:
${beforeText}

Text after:
${afterText}

Provide the text that should go between these segments:`;
}

/**
 * Creates a prompt for the AI to insert text at the current cursor position.
 * @param beforeText The text before the cursor.
 * @param afterText The text after the cursor.
 * @returns The formatted cursor insert prompt.
 */
export function createCursorInsertPrompt(beforeText: string, afterText: string): string {
  return `Provide text that would naturally fit at the cursor position between the following two text segments. Write in the same style and tone as the surrounding text. IMPORTANT: Respect all whitespace in the provided text exactly as it appears, including any trailing or leading spaces. Your response should connect naturally with the existing whitespace. Only provide the text to insert, nothing else.

Text before cursor:
${beforeText}

Text after cursor:
${afterText}

Provide the text to insert at the cursor position:`;
}
